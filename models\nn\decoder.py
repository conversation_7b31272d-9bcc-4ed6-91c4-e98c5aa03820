from .base_arch import ResML<PERSON>, ConvTransposeModule
import torch.nn as nn


def create_brain_decoder(in_dim_brain, h, num_mlp=8):
    brain_decoder = nn.Sequential(
                nn.Linear(h, h),
                nn.LayerNorm(h),
                nn.<PERSON><PERSON>(),
                ResMLP(h, num_mlp), 
                nn.Linear(h, in_dim_brain),
                )
    return brain_decoder
        
def create_clip_decoder(h, reduce_channel, channel, clip_size=768):
    clip_decoder = nn.Sequential(
        nn.Linear(h, h),
        nn.LayerNorm(h),
        nn.GELU(),
        #ConvTransposeModule(reduce_channel, channel, h, clip_size) 
        nn.Linear(h, clip_size)
    )
    return clip_decoder

def create_union_decoder(h):
    union_decoder = nn.Sequential(
        nn.Linear(h//2, h//2),
        nn.LayerNorm(h//2),
        nn.GELU(),
        nn.Dropout(0.2),
        nn.Linear(h//2, h),
    )
    return union_decoder

def create_sub_decoder(len_subj, sub_dim):
    sub_decoder = nn.Sequential(
            nn.Linear(sub_dim, sub_dim),
            nn.LayerNorm(sub_dim),
            nn.GELU(),
            nn.Linear(sub_dim, sub_dim),
            nn.LayerNorm(sub_dim),
            nn.GELU(),
            nn.Linear(sub_dim, len_subj),
        )
    return sub_decoder

def create_modality_decoder(num_mod, sub_dim):
    modality_decoder = nn.Sequential(
            nn.Linear(sub_dim, sub_dim),
            nn.LayerNorm(sub_dim),
            nn.GELU(),
            nn.Linear(sub_dim, sub_dim),
            nn.LayerNorm(sub_dim),
            nn.GELU(),
            nn.Linear(sub_dim, num_mod),
        )
    return modality_decoder