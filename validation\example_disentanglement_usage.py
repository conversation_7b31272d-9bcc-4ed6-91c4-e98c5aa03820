"""
使用示例：如何在mmvaePlus模型中使用解耦指标评估

这个脚本展示了如何集成解耦评估到您的测试流程中
"""

import torch
import numpy as np
from validation.test_helper import TestBase
from validation.disentanglement_evaluator import DisentanglementEvaluator


def enhanced_test_epoch_with_disentanglement(test_instance):
    """
    增强版本的test_epoch，添加解耦评估
    
    Args:
        test_instance: TestBase的实例
    """
    
    # 执行原始的测试流程
    print("Running original test epoch...")
    test_instance.test_epoch()
    
    # 添加解耦评估
    print("\n" + "="*60)
    print("Adding Disentanglement Evaluation...")
    print("="*60)
    
    try:
        # 执行解耦评估
        disentanglement_results = test_instance.evaluate_disentanglement_metrics()
        
        if disentanglement_results:
            print(f"Disentanglement evaluation completed successfully!")
            
            # 打印详细结果
            print("\n" + "="*50)
            print("DISENTANGLEMENT EVALUATION RESULTS")
            print("="*50)
            
            for metric_name, score in disentanglement_results.items():
                print(f"{metric_name:<25}: {score:.4f}")
            
            # 解释结果
            print("\n" + "="*50)
            print("METRIC INTERPRETATIONS")
            print("="*50)
            print("MIG (Mutual Information Gap):")
            print("  - Range: [0, 1], Higher is better")
            print("  - Measures how well each factor is captured by a single latent dimension")
            print(f"  - Your score: {disentanglement_results.get('MIG', 0):.4f}")
            
            print("\nSAP (Separated Attribute Predictability):")
            print("  - Range: [0, 1], Higher is better") 
            print("  - Measures how well attributes are separated in the latent space")
            print(f"  - Your score: {disentanglement_results.get('SAP', 0):.4f}")
            
            print("\nDCI Metrics:")
            print("  - Disentanglement: How well each factor is captured by one dimension")
            print(f"    Your score: {disentanglement_results.get('DCI_Disentanglement', 0):.4f}")
            print("  - Completeness: How well each dimension captures one factor")
            print(f"    Your score: {disentanglement_results.get('DCI_Completeness', 0):.4f}")
            print("  - Informativeness: How much information is preserved")
            print(f"    Your score: {disentanglement_results.get('DCI_Informativeness', 0):.4f}")
            
            print("\nModality Specificity:")
            print("  - How well modality-specific representations capture modality info")
            print(f"  - Your score: {disentanglement_results.get('Modality_Specificity', 0):.4f}")
            
            print("\nSubject Specificity:")
            print("  - How well subject-specific representations capture subject info")
            print(f"  - Your score: {disentanglement_results.get('Subject_Specificity', 0):.4f}")
            
            print("\nSemantic Consistency:")
            print("  - How well shared representations capture semantic info across modalities")
            print(f"  - Your score: {disentanglement_results.get('Semantic_Consistency', 0):.4f}")
            
            print("="*50)
            
            return disentanglement_results
            
        else:
            print("Disentanglement evaluation failed - insufficient data")
            return None
            
    except Exception as e:
        print(f"Error during disentanglement evaluation: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def standalone_disentanglement_evaluation(representations_dict):
    """
    独立的解耦评估函数，可以直接传入表示数据
    
    Args:
        representations_dict: 包含以下键的字典
            - 'shared_representations': {modality: array} 共享表示
            - 'modality_specific_representations': {modality: array} 模态特定表示
            - 'subject_specific_representations': {modality: array} 被试特定表示
            - 'ground_truth_factors': {factor_name: array} 真实因子标签
    
    Returns:
        dict: 解耦评估结果
    """
    
    print("Starting standalone disentanglement evaluation...")
    
    # 初始化评估器
    evaluator = DisentanglementEvaluator()
    
    # 执行评估
    results = evaluator.evaluate_all_metrics(representations_dict)
    
    return results


def create_example_representations():
    """
    创建示例表示数据用于测试
    """
    np.random.seed(42)
    
    n_samples = 1000
    shared_dim = 64
    modality_dim = 32
    subject_dim = 16
    
    # 创建示例数据
    representations = {
        'shared_representations': {
            'image': np.random.randn(n_samples, shared_dim),
            'text': np.random.randn(n_samples, shared_dim),
            'fmri': np.random.randn(n_samples, shared_dim),
        },
        'modality_specific_representations': {
            'image': np.random.randn(n_samples, modality_dim),
            'text': np.random.randn(n_samples, modality_dim),
            'fmri': np.random.randn(n_samples, modality_dim),
        },
        'subject_specific_representations': {
            'all': np.random.randn(n_samples, subject_dim),
        },
        'ground_truth_factors': {
            'semantic': np.random.randint(0, 10, n_samples),  # 10个语义类别
            'modality': np.random.randint(0, 3, n_samples),   # 3个模态
            'subject': np.random.randint(0, 5, n_samples),    # 5个被试
        }
    }
    
    return representations


if __name__ == "__main__":
    # 示例1：使用示例数据进行测试
    print("Example 1: Testing with synthetic data")
    example_representations = create_example_representations()
    results = standalone_disentanglement_evaluation(example_representations)
    
    if results:
        print("\nResults:")
        for metric, score in results.items():
            print(f"{metric}: {score:.4f}")
    
    # 示例2：如何在实际测试中使用
    print("\n" + "="*60)
    print("Example 2: How to integrate into your test pipeline")
    print("="*60)
    print("""
    # 在您的测试脚本中：
    
    from validation.test_helper import TestBase
    from validation.example_disentanglement_usage import enhanced_test_epoch_with_disentanglement
    
    # 创建测试实例
    test_instance = TestBase(args, accelerator, voxel2clip, device)
    test_instance.load()
    
    # 运行增强的测试（包含解耦评估）
    results = enhanced_test_epoch_with_disentanglement(test_instance)
    
    # 或者直接调用解耦评估
    disentanglement_results = test_instance.evaluate_disentanglement_metrics()
    """)
