"""
测试解耦评估系统的功能
"""

import numpy as np
import torch
from validation.disentanglement_evaluator import DisentanglementEvaluator


def create_test_data():
    """
    创建测试数据来验证解耦评估系统
    """
    np.random.seed(42)
    
    n_samples = 500
    shared_dim = 32
    modality_dim = 16
    subject_dim = 8
    
    # 创建具有一定结构的测试数据
    n_semantic_classes = 5
    n_modalities = 3
    n_subjects = 4
    
    # 生成标签
    semantic_labels = np.random.randint(0, n_semantic_classes, n_samples)
    modality_labels = np.random.randint(0, n_modalities, n_samples)
    subject_labels = np.random.randint(0, n_subjects, n_samples)
    
    # 生成具有一定结构的表示
    # 共享表示应该与语义标签相关
    shared_base = np.random.randn(n_semantic_classes, shared_dim)
    shared_representations = {}
    
    for mod_name in ['image', 'text', 'fmri']:
        shared_repr = np.zeros((n_samples, shared_dim))
        for i in range(n_samples):
            semantic_class = semantic_labels[i]
            shared_repr[i] = shared_base[semantic_class] + 0.1 * np.random.randn(shared_dim)
        shared_representations[mod_name] = shared_repr
    
    # 模态特定表示应该与模态标签相关
    modality_base = np.random.randn(n_modalities, modality_dim)
    modality_representations = {}
    
    for mod_idx, mod_name in enumerate(['image', 'text', 'fmri']):
        modality_repr = np.zeros((n_samples, modality_dim))
        for i in range(n_samples):
            if modality_labels[i] == mod_idx:
                modality_repr[i] = modality_base[mod_idx] + 0.1 * np.random.randn(modality_dim)
            else:
                modality_repr[i] = 0.1 * np.random.randn(modality_dim)
        modality_representations[mod_name] = modality_repr
    
    # 被试特定表示应该与被试标签相关
    subject_base = np.random.randn(n_subjects, subject_dim)
    subject_repr = np.zeros((n_samples, subject_dim))
    for i in range(n_samples):
        subject_class = subject_labels[i]
        subject_repr[i] = subject_base[subject_class] + 0.1 * np.random.randn(subject_dim)
    
    representations = {
        'shared_representations': shared_representations,
        'modality_specific_representations': modality_representations,
        'subject_specific_representations': {'all': subject_repr},
        'ground_truth_factors': {
            'semantic': semantic_labels,
            'modality': modality_labels,
            'subject': subject_labels,
        }
    }
    
    return representations


def test_individual_metrics():
    """
    测试各个指标的计算
    """
    print("Testing individual metrics...")
    
    evaluator = DisentanglementEvaluator()
    representations = create_test_data()
    
    # 测试MIG
    print("Testing MIG...")
    mig_score = evaluator.compute_mig(representations)
    print(f"MIG Score: {mig_score:.4f}")
    assert 0 <= mig_score <= 1, f"MIG score should be in [0,1], got {mig_score}"
    
    # 测试SAP
    print("Testing SAP...")
    sap_score = evaluator.compute_sap(representations)
    print(f"SAP Score: {sap_score:.4f}")
    assert 0 <= sap_score <= 1, f"SAP score should be in [0,1], got {sap_score}"
    
    # 测试DCI
    print("Testing DCI...")
    dci_scores = evaluator.compute_dci(representations)
    print(f"DCI Scores: {dci_scores}")
    for metric, score in dci_scores.items():
        assert 0 <= score <= 1, f"{metric} should be in [0,1], got {score}"
    
    # 测试特异性指标
    print("Testing specificity metrics...")
    mod_spec = evaluator.compute_modality_specificity(representations)
    subj_spec = evaluator.compute_subject_specificity(representations)
    sem_cons = evaluator.compute_semantic_consistency(representations)
    
    print(f"Modality Specificity: {mod_spec:.4f}")
    print(f"Subject Specificity: {subj_spec:.4f}")
    print(f"Semantic Consistency: {sem_cons:.4f}")
    
    assert 0 <= mod_spec <= 1, f"Modality specificity should be in [0,1], got {mod_spec}"
    assert 0 <= subj_spec <= 1, f"Subject specificity should be in [0,1], got {subj_spec}"
    assert 0 <= sem_cons <= 1, f"Semantic consistency should be in [0,1], got {sem_cons}"
    
    print("✓ All individual metrics passed!")


def test_full_evaluation():
    """
    测试完整的评估流程
    """
    print("\nTesting full evaluation pipeline...")
    
    evaluator = DisentanglementEvaluator()
    representations = create_test_data()
    
    # 执行完整评估
    results = evaluator.evaluate_all_metrics(representations)
    
    print("Full evaluation results:")
    for metric_name, score in results.items():
        print(f"  {metric_name}: {score:.4f}")
        assert 0 <= score <= 1, f"{metric_name} should be in [0,1], got {score}"
    
    # 检查是否包含所有期望的指标
    expected_metrics = [
        'MIG', 'SAP', 'DCI_Disentanglement', 'DCI_Completeness', 
        'DCI_Informativeness', 'Modality_Specificity', 
        'Subject_Specificity', 'Semantic_Consistency'
    ]
    
    for metric in expected_metrics:
        assert metric in results, f"Missing metric: {metric}"
    
    print("✓ Full evaluation pipeline passed!")


def test_edge_cases():
    """
    测试边界情况
    """
    print("\nTesting edge cases...")
    
    evaluator = DisentanglementEvaluator()
    
    # 测试空数据
    empty_representations = {
        'shared_representations': {},
        'modality_specific_representations': {},
        'subject_specific_representations': {},
        'ground_truth_factors': {}
    }
    
    results = evaluator.evaluate_all_metrics(empty_representations)
    print("Empty data results:", results)
    
    # 测试单一模态
    single_mod_representations = {
        'shared_representations': {'image': np.random.randn(100, 32)},
        'modality_specific_representations': {'image': np.random.randn(100, 16)},
        'subject_specific_representations': {'all': np.random.randn(100, 8)},
        'ground_truth_factors': {
            'semantic': np.random.randint(0, 5, 100),
            'modality': np.zeros(100),  # 单一模态
            'subject': np.random.randint(0, 3, 100),
        }
    }
    
    results = evaluator.evaluate_all_metrics(single_mod_representations)
    print("Single modality results:", results)
    
    print("✓ Edge cases handled correctly!")


def test_with_perfect_disentanglement():
    """
    测试理想解耦情况下的分数
    """
    print("\nTesting with perfect disentanglement...")
    
    n_samples = 400
    n_factors = 4
    factor_dim = 10
    
    # 创建完美解耦的数据
    perfect_representations = {
        'shared_representations': {},
        'modality_specific_representations': {},
        'subject_specific_representations': {},
        'ground_truth_factors': {}
    }
    
    # 每个因子对应一个独立的维度
    semantic_labels = np.random.randint(0, n_factors, n_samples)
    modality_labels = np.random.randint(0, 2, n_samples)
    subject_labels = np.random.randint(0, 3, n_samples)
    
    # 创建完美解耦的共享表示
    shared_repr = np.zeros((n_samples, factor_dim))
    for i in range(n_samples):
        shared_repr[i, semantic_labels[i]] = 1.0  # 只有对应维度为1
    
    perfect_representations['shared_representations']['image'] = shared_repr
    perfect_representations['shared_representations']['text'] = shared_repr
    
    # 创建完美解耦的模态特定表示
    modality_repr = np.zeros((n_samples, 2))
    for i in range(n_samples):
        modality_repr[i, modality_labels[i]] = 1.0
    
    perfect_representations['modality_specific_representations']['image'] = modality_repr
    perfect_representations['modality_specific_representations']['text'] = modality_repr
    
    # 创建完美解耦的被试特定表示
    subject_repr = np.zeros((n_samples, 3))
    for i in range(n_samples):
        subject_repr[i, subject_labels[i]] = 1.0
    
    perfect_representations['subject_specific_representations']['all'] = subject_repr
    
    perfect_representations['ground_truth_factors'] = {
        'semantic': semantic_labels,
        'modality': modality_labels,
        'subject': subject_labels,
    }
    
    evaluator = DisentanglementEvaluator()
    results = evaluator.evaluate_all_metrics(perfect_representations)
    
    print("Perfect disentanglement results:")
    for metric_name, score in results.items():
        print(f"  {metric_name}: {score:.4f}")
    
    # 理想情况下，大部分指标应该很高
    assert results['Modality_Specificity'] > 0.8, "Perfect modality specificity should be high"
    assert results['Subject_Specificity'] > 0.8, "Perfect subject specificity should be high"
    
    print("✓ Perfect disentanglement test passed!")


if __name__ == "__main__":
    print("=" * 60)
    print("Testing Disentanglement Evaluation System")
    print("=" * 60)
    
    try:
        test_individual_metrics()
        test_full_evaluation()
        test_edge_cases()
        test_with_perfect_disentanglement()
        
        print("\n" + "=" * 60)
        print("✅ ALL TESTS PASSED!")
        print("The disentanglement evaluation system is working correctly.")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
