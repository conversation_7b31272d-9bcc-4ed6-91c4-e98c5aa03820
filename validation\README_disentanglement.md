# mmvaePlus 解耦指标评估系统

这个系统专门为您的mmvaePlus模型设计，用于评估多模态变分自编码器的解耦表示质量。

## 概述

mmvaePlus模型学习三种类型的解耦表示：
- **u (共享表示)**: 跨模态共享的语义信息
- **w (模态特定表示)**: 每个模态独有的信息  
- **s (被试特定表示)**: 被试相关的个体差异信息

本评估系统提供了多种指标来量化这些表示的解耦质量。

## 文件结构

```
validation/
├── disentanglement_evaluator.py    # 核心评估器
├── test_helper.py                   # 增强的测试基类（已修改）
├── example_disentanglement_usage.py # 使用示例
└── README_disentanglement.md       # 本文档
```

## 评估指标

### 1. MIG (Mutual Information Gap)
- **范围**: [0, 1]，越高越好
- **含义**: 衡量每个真实因子是否主要由单个潜在维度捕获
- **解释**: 高MIG表示模型学到了良好的解耦表示

### 2. SAP (Separated Attribute Predictability)  
- **范围**: [0, 1]，越高越好
- **含义**: 衡量属性在潜在空间中的分离程度
- **解释**: 高SAP表示不同属性被很好地分离

### 3. DCI 指标组
- **Disentanglement**: 每个因子是否由单个维度捕获
- **Completeness**: 每个维度是否只捕获单个因子
- **Informativeness**: 保留了多少原始信息

### 4. 模态特异性 (Modality Specificity)
- **含义**: 模态特定表示(w)是否真的捕获了模态特异信息
- **评估**: 使用w预测模态标签的准确性

### 5. 被试特异性 (Subject Specificity)
- **含义**: 被试特定表示(s)是否真的捕获了被试特异信息
- **评估**: 使用s预测被试标签的准确性

### 6. 语义一致性 (Semantic Consistency)
- **含义**: 共享表示(u)在不同模态间的语义一致性
- **评估**: 使用u预测语义标签的准确性

## 使用方法

### 方法1：集成到现有测试流程

```python
# 在您的测试脚本中
from validation.test_helper import TestBase

# 创建测试实例
test_instance = TestBase(args, accelerator, voxel2clip, device)
test_instance.load()

# 运行测试（现在自动包含解耦评估）
test_instance.test()
```

解耦评估会自动在`test_epoch()`方法中执行，结果会写入日志文件。

### 方法2：单独调用解耦评估

```python
# 在测试过程中单独调用
disentanglement_results = test_instance.evaluate_disentanglement_metrics()

if disentanglement_results:
    for metric_name, score in disentanglement_results.items():
        print(f"{metric_name}: {score:.4f}")
```

### 方法3：使用自定义数据

```python
from validation.disentanglement_evaluator import DisentanglementEvaluator

# 准备您的表示数据
representations = {
    'shared_representations': {
        'image': image_u_embeddings,    # 图像的u表示
        'text': text_u_embeddings,      # 文本的u表示
        'fmri': fmri_u_embeddings,      # fMRI的u表示
    },
    'modality_specific_representations': {
        'image': image_w_embeddings,    # 图像的w表示
        'text': text_w_embeddings,      # 文本的w表示
        'fmri': fmri_w_embeddings,      # fMRI的w表示
    },
    'subject_specific_representations': {
        'all': s_embeddings,            # 所有的s表示
    },
    'ground_truth_factors': {
        'semantic': semantic_labels,    # 语义标签
        'modality': modality_labels,    # 模态标签
        'subject': subject_labels,      # 被试标签
    }
}

# 执行评估
evaluator = DisentanglementEvaluator()
results = evaluator.evaluate_all_metrics(representations)
```

## 数据要求

### 表示数据格式
- **共享表示**: `{modality: numpy.array}` - 每个模态的u表示
- **模态特定表示**: `{modality: numpy.array}` - 每个模态的w表示  
- **被试特定表示**: `{modality: numpy.array}` - s表示

### 标签数据格式
- **语义标签**: 整数数组，表示语义类别（如聚类标签）
- **模态标签**: 整数数组，表示模态类型（0=image, 1=text, 2=fmri, 3=eeg）
- **被试标签**: 整数数组，表示被试ID

## 结果解释

### 良好的解耦表示应该具有：
1. **高MIG分数** (>0.5): 表示因子被很好地分离
2. **高SAP分数** (>0.5): 表示属性被很好地分离
3. **高DCI分数**: 
   - Disentanglement > 0.7
   - Completeness > 0.7  
   - Informativeness > 0.8
4. **高模态特异性** (>0.8): w确实捕获了模态信息
5. **高被试特异性** (>0.8): s确实捕获了被试信息
6. **高语义一致性** (>0.7): u确实捕获了语义信息

### 如果分数较低，可能的原因：
- 模型训练不充分
- 超参数设置不当（如β值）
- 数据质量问题
- 模型架构需要调整

## 注意事项

1. **数据量**: 评估需要足够的样本数（建议>100个样本每个因子）
2. **标签质量**: 真实因子标签的质量直接影响评估结果
3. **计算时间**: 某些指标（如MIG）计算较慢，请耐心等待
4. **随机性**: 使用了随机森林等方法，结果可能有小幅波动

## 故障排除

### 常见问题：

1. **"No feature data available"**: 
   - 确保模型forward返回了embeddings
   - 检查test_helper中的数据提取逻辑

2. **"Insufficient data"**:
   - 增加测试样本数量
   - 检查标签数据是否正确

3. **评估结果全为0**:
   - 检查表示数据和标签的对应关系
   - 确认数据格式正确

4. **内存不足**:
   - 减少样本数量或使用更小的批次
   - 考虑使用更高效的互信息估计方法

## 扩展

您可以通过以下方式扩展评估系统：

1. **添加新指标**: 在`DisentanglementEvaluator`中添加新的评估方法
2. **自定义因子**: 在`_build_ground_truth_factors`中添加新的真实因子
3. **可视化**: 添加t-SNE/UMAP可视化来直观展示解耦质量

## 参考文献

1. Chen et al. "Isolating Sources of Disentanglement in Variational Autoencoders" (MIG)
2. Kumar et al. "Variational Inference of Disentangled Latent Concepts from Unlabeled Observations" (SAP)  
3. Eastwood & Williams "A Framework for the Quantitative Evaluation of Disentangled Representations" (DCI)
