batch_size=200
val_batch_size=50
num_epochs=600
mse_mult=1000
rec_mult=1
cyc_mult=0

model_name="MIDAS"
exp_name=$model_name'_fmri15_5wm.5_eeg78'
load_from="/root/workspace/multi_vae/train_logs_fmri_eeg/$exp_name/last.pth"
use_modalities=("fmri" "image" "text" "eeg")
dataset_name="nsd"
gpu_id=0


CUDA_VISIBLE_DEVICES=$gpu_id python -W ignore \
test.py \
--model_name $model_name --exp_name $exp_name --dataset_name $dataset_name --fmri_subj_list 1 5 --eeg_subj_list 7 8 \
--fmri_trainer_list 1 5 --eeg_trainer_list 7 8 \
--num_epochs $num_epochs --batch_size $batch_size --val_batch_size $val_batch_size \
--h_size 2048 --n_blocks 4 --pool_type max --pool_num 8192 --eeg_size 1900 \
--mse_mult $mse_mult --rec_mult $rec_mult --cyc_mult $cyc_mult \
--eval_interval 2 --ckpt_interval 2 \
--load_from $load_from --num_workers 8 \
--max_lr 1.5e-4  --use_modalities "${use_modalities[@]}"