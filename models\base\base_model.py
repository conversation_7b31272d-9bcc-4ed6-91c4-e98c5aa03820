import torch
import torch.nn as nn
import torch.distributions as dist
from copy import deepcopy
from .base_utils import cross_entropy
import numpy as np

class BaseModel(nn.Module):
    def __init__(self, model_config, encoders, decoders):
        super().__init__()

        print('this is base model')

        self.encoders = encoders
        self.decoders = decoders
        self.used_modalities = model_config.use_modalities
        self.n_modalities = len(self.used_modalities)

        self.set_decoders(decoders)
        self.set_encoders(encoders)

        self.model_config = model_config
        self.latent_dim = model_config.latent_dim

        self.input_dims = model_config.input_dims
        self.use_likelihood_rescaling = model_config.uses_likelihood_rescaling
        if self.use_likelihood_rescaling:
            if self.model_config.rescale_factors is not None:
                self.rescale_factors = model_config.rescale_factors
            elif self.input_dims is None:
                raise AttributeError(
                    " inputs_dim = None but (use_likelihood_rescaling = True"
                    " in model_config)"
                    " To compute likelihood rescalings we need the input dimensions."
                    " Please provide a valid dictionary for input_dims."
                )
            else:
                max_dim = max(*[np.prod(self.input_dims[k]) for k in self.input_dims])
                self.rescale_factors = {
                    k: max_dim / np.prod(self.input_dims[k]) for k in self.input_dims
                }
        else:
            #self.rescale_factors = {k: 1 for k in self.encoders}
            self.rescale_factors = {'fmri':1, 'eeg':1, 'image':10000, 'text':10000}
            # above, we take the modalities keys in self.encoders as input_dims may be None

        # Set the reconstruction losses
        if model_config.decoders_dist is None:
            model_config.decoders_dist = {k: "normal" for k in self.encoders}
        if model_config.decoder_dist_params is None:
            model_config.decoder_dist_params = {}
        self.set_decoders_dist(
            model_config.decoders_dist, deepcopy(model_config.decoder_dist_params)
        )


    def set_encoders(self, encoders: dict) -> None:
        """Set the encoders of the model"""
        self.encoders = nn.ModuleDict()
        for modality in encoders:
            encoder = encoders[modality]
            self.encoders[modality] = encoder

    def set_decoders(self, decoders: dict) -> None:
        """Set the decoders of the model"""
        self.decoders = nn.ModuleDict()
        for modality in decoders:
            decoder = decoders[modality]
            self.decoders[modality] = decoder
    

    def encoding(self, x, x_type, style_dim=0, subj_dim=0):
        x = self.encoders[x_type](x)
        x = self.encoders['union'](x)
        x_mu, x_logvar = torch.chunk(x, 2, dim=1)

        emb_dict = {}

        if subj_dim != 0:
            h_dim = x_mu.shape[1]
            x_mu, x_mu_subj = x_mu.split([h_dim-subj_dim, subj_dim], dim=1)
            x_logvar, x_logvar_subj = x_logvar.split([h_dim-subj_dim, subj_dim], dim=1)
            emb_dict['subj_embedding'] = x_mu_subj
            emb_dict['subj_covariance'] = x_logvar_subj

        if style_dim != 0:
            h_dim = x_mu.shape[1]
            # print('h_dim.shape', h_dim)
            x_mu_share, x_mu_style = x_mu.split([h_dim-style_dim, style_dim], dim=1)
            x_logvar_share, x_logvar_style = x_logvar.split([h_dim-style_dim, style_dim], dim=1)

            emb_dict['embedding'] = x_mu_share
            emb_dict['log_covariance'] =  x_logvar_share
            emb_dict['style_embedding'] = x_mu_style 
            emb_dict['style_log_covariance'] = x_logvar_style
        else:
            emb_dict['embedding'] = x_mu
            emb_dict['log_covariance'] = x_logvar

        return emb_dict
    
    
    def decoding(self, x, x_type):
        x = self.decoders['union'](x)
        x = self.decoders[x_type](x)
        return x
    

    def set_decoders_dist(self, recon_dict, dist_params_dict):
        """Set the reconstruction losses functions decoders_dist
        and the log_probabilites functions recon_log_probs.
        recon_log_probs is the normalized negative version of recon_loss and is used only for
        likelihood estimation.
        """
        self.recon_log_probs = {}

        for k in recon_dict:
            if recon_dict[k] == "normal":
                params_mod = dist_params_dict.pop(k, {})
                scale = params_mod.pop("scale", 1.0)
                self.recon_log_probs[k] = lambda input, target: dist.Normal(
                    input, scale
                ).log_prob(target)

            elif recon_dict[k] == "bernoulli":
                self.recon_log_probs[k] = lambda input, target: dist.Bernoulli(
                    logits=input
                ).log_prob(target)

            elif recon_dict[k] == "laplace":
                params_mod = dist_params_dict.pop(k, {})
                scale = params_mod.pop("scale", 1.0)
                self.recon_log_probs[k] = lambda input, target: dist.Laplace(
                    input, scale
                ).log_prob(target)

            elif recon_dict[k] == "categorical":
                self.recon_log_probs[k] = lambda input, target: cross_entropy(
                    input, target
                )