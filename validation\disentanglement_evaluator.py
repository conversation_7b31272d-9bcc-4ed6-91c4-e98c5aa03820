import torch
import numpy as np
from tqdm import tqdm
from scipy.stats import entropy as scipy_entropy
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import LabelEncoder
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.metrics import r2_score, accuracy_score
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')


class DisentanglementEvaluator:
    """
    专门为mmvaePlus模型设计的解耦评估器
    评估共享表示(u)、模态特定表示(w)、被试特定表示(s)的解耦质量
    """
    
    def __init__(self, n_bins=64, random_state=42):
        self.n_bins = n_bins
        self.random_state = random_state
        
    def evaluate_all_metrics(self, representations):
        """
        评估所有解耦指标
        
        Args:
            representations: 包含以下键的字典
                - 'shared_representations': {modality: array} 共享表示
                - 'modality_specific_representations': {modality: array} 模态特定表示
                - 'subject_specific_representations': {modality: array} 被试特定表示
                - 'ground_truth_factors': {factor_name: array} 真实因子标签
        """
        results = {}
        
        print("Evaluating disentanglement metrics...")
        
        # 1. MIG (Mutual Information Gap) - 衡量整体解耦质量
        print("Computing MIG...")
        mig_score = self.compute_mig(representations)
        results['MIG'] = mig_score
        
        # 2. SAP (Separated Attribute Predictability) - 衡量属性分离度
        print("Computing SAP...")
        sap_score = self.compute_sap(representations)
        results['SAP'] = sap_score
        
        # 3. DCI (Disentanglement, Completeness, Informativeness) - 综合评估
        print("Computing DCI...")
        dci_scores = self.compute_dci(representations)
        results.update(dci_scores)
        
        # 4. 模态特异性评估 - 评估模态特定表示的质量
        print("Computing Modality Specificity...")
        modality_specificity = self.compute_modality_specificity(representations)
        results['Modality_Specificity'] = modality_specificity
        
        # 5. 被试特异性评估 - 评估被试特定表示的质量
        print("Computing Subject Specificity...")
        subject_specificity = self.compute_subject_specificity(representations)
        results['Subject_Specificity'] = subject_specificity
        
        # 6. 语义一致性评估 - 评估共享表示的语义一致性
        print("Computing Semantic Consistency...")
        semantic_consistency = self.compute_semantic_consistency(representations)
        results['Semantic_Consistency'] = semantic_consistency
        
        return results
    
    def compute_mig(self, representations):
        """
        计算MIG (Mutual Information Gap)
        """
        # 合并所有表示类型
        all_representations = {}
        
        # 添加共享表示
        for modality, repr_data in representations['shared_representations'].items():
            all_representations[f'shared_{modality}'] = repr_data
            
        # 添加模态特定表示
        for modality, repr_data in representations['modality_specific_representations'].items():
            all_representations[f'modality_{modality}'] = repr_data
            
        # 添加被试特定表示
        for modality, repr_data in representations['subject_specific_representations'].items():
            all_representations[f'subject_{modality}'] = repr_data
        
        if not all_representations or not representations['ground_truth_factors']:
            return 0.0
        
        # 计算互信息矩阵
        factors = list(representations['ground_truth_factors'].keys())
        repr_names = list(all_representations.keys())
        
        mi_matrix = np.zeros((len(factors), len(repr_names)))
        
        for i, factor_name in enumerate(factors):
            factor_labels = representations['ground_truth_factors'][factor_name]
            
            for j, repr_name in enumerate(repr_names):
                repr_data = all_representations[repr_name]
                
                # 确保样本数量匹配
                min_samples = min(len(factor_labels), len(repr_data))
                if min_samples == 0:
                    continue
                    
                factor_subset = factor_labels[:min_samples]
                repr_subset = repr_data[:min_samples]
                
                # 计算互信息
                mi = self._compute_mutual_information(repr_subset, factor_subset)
                mi_matrix[i, j] = mi
        
        # 计算MIG
        mig_scores = []
        for i in range(len(factors)):
            mi_row = mi_matrix[i, :]
            if len(mi_row) < 2:
                continue
                
            sorted_mi = np.sort(mi_row)[::-1]
            if len(sorted_mi) >= 2:
                gap = sorted_mi[0] - sorted_mi[1]
                entropy_factor = self._compute_entropy(representations['ground_truth_factors'][factors[i]])
                if entropy_factor > 0:
                    mig_scores.append(gap / entropy_factor)
        
        return np.mean(mig_scores) if mig_scores else 0.0
    
    def compute_sap(self, representations):
        """
        计算SAP (Separated Attribute Predictability)
        """
        if not representations['ground_truth_factors']:
            return 0.0
        
        sap_scores = []
        
        # 对每个真实因子计算SAP
        for factor_name, factor_labels in representations['ground_truth_factors'].items():
            factor_sap_scores = []
            
            # 对每种表示类型计算预测能力
            for repr_type in ['shared_representations', 'modality_specific_representations', 'subject_specific_representations']:
                if repr_type not in representations:
                    continue
                    
                for modality, repr_data in representations[repr_type].items():
                    # 确保样本数量匹配
                    min_samples = min(len(factor_labels), len(repr_data))
                    if min_samples < 10:  # 需要足够的样本
                        continue
                    
                    factor_subset = factor_labels[:min_samples]
                    repr_subset = repr_data[:min_samples]
                    
                    # 使用随机森林预测因子
                    score = self._predict_factor_from_representation(repr_subset, factor_subset)
                    factor_sap_scores.append(score)
            
            if factor_sap_scores:
                # SAP是最高预测分数与第二高分数的差值
                sorted_scores = sorted(factor_sap_scores, reverse=True)
                if len(sorted_scores) >= 2:
                    sap = sorted_scores[0] - sorted_scores[1]
                else:
                    sap = sorted_scores[0]
                sap_scores.append(sap)
        
        return np.mean(sap_scores) if sap_scores else 0.0
    
    def compute_dci(self, representations):
        """
        计算DCI (Disentanglement, Completeness, Informativeness)
        """
        if not representations['ground_truth_factors']:
            return {'DCI_Disentanglement': 0.0, 'DCI_Completeness': 0.0, 'DCI_Informativeness': 0.0}
        
        # 合并所有表示
        all_reprs = []
        repr_names = []
        
        for repr_type in ['shared_representations', 'modality_specific_representations', 'subject_specific_representations']:
            if repr_type in representations:
                for modality, repr_data in representations[repr_type].items():
                    all_reprs.append(repr_data)
                    repr_names.append(f"{repr_type}_{modality}")
        
        if not all_reprs:
            return {'DCI_Disentanglement': 0.0, 'DCI_Completeness': 0.0, 'DCI_Informativeness': 0.0}
        
        # 找到最小样本数
        min_samples = min(len(repr_data) for repr_data in all_reprs)
        if min_samples < 10:
            return {'DCI_Disentanglement': 0.0, 'DCI_Completeness': 0.0, 'DCI_Informativeness': 0.0}
        
        # 截断到相同长度
        all_reprs = [repr_data[:min_samples] for repr_data in all_reprs]
        
        # 计算重要性矩阵
        importance_matrix = np.zeros((len(representations['ground_truth_factors']), len(all_reprs)))
        
        for i, (factor_name, factor_labels) in enumerate(representations['ground_truth_factors'].items()):
            factor_subset = factor_labels[:min_samples]
            
            for j, repr_data in enumerate(all_reprs):
                # 使用随机森林计算特征重要性
                importance = self._compute_feature_importance(repr_data, factor_subset)
                importance_matrix[i, j] = importance
        
        # 计算DCI指标
        disentanglement = self._compute_disentanglement(importance_matrix)
        completeness = self._compute_completeness(importance_matrix)
        informativeness = np.mean(np.max(importance_matrix, axis=1))
        
        return {
            'DCI_Disentanglement': disentanglement,
            'DCI_Completeness': completeness,
            'DCI_Informativeness': informativeness
        }
    
    def compute_modality_specificity(self, representations):
        """
        评估模态特定表示是否真的捕获了模态特异信息
        """
        if 'modality_specific_representations' not in representations or 'modality' not in representations['ground_truth_factors']:
            return 0.0
        
        modality_labels = representations['ground_truth_factors']['modality']
        specificity_scores = []
        
        for modality, repr_data in representations['modality_specific_representations'].items():
            min_samples = min(len(modality_labels), len(repr_data))
            if min_samples < 10:
                continue
            
            modality_subset = modality_labels[:min_samples]
            repr_subset = repr_data[:min_samples]
            
            # 预测模态标签
            score = self._predict_factor_from_representation(repr_subset, modality_subset)
            specificity_scores.append(score)
        
        return np.mean(specificity_scores) if specificity_scores else 0.0
    
    def compute_subject_specificity(self, representations):
        """
        评估被试特定表示是否真的捕获了被试特异信息
        """
        if 'subject_specific_representations' not in representations or 'subject' not in representations['ground_truth_factors']:
            return 0.0
        
        subject_labels = representations['ground_truth_factors']['subject']
        specificity_scores = []
        
        for modality, repr_data in representations['subject_specific_representations'].items():
            min_samples = min(len(subject_labels), len(repr_data))
            if min_samples < 10:
                continue
            
            subject_subset = subject_labels[:min_samples]
            repr_subset = repr_data[:min_samples]
            
            # 预测被试标签
            score = self._predict_factor_from_representation(repr_subset, subject_subset)
            specificity_scores.append(score)
        
        return np.mean(specificity_scores) if specificity_scores else 0.0
    
    def compute_semantic_consistency(self, representations):
        """
        评估共享表示在不同模态间的语义一致性
        """
        if 'shared_representations' not in representations or 'semantic' not in representations['ground_truth_factors']:
            return 0.0
        
        semantic_labels = representations['ground_truth_factors']['semantic']
        consistency_scores = []
        
        for modality, repr_data in representations['shared_representations'].items():
            min_samples = min(len(semantic_labels), len(repr_data))
            if min_samples < 10:
                continue
            
            semantic_subset = semantic_labels[:min_samples]
            repr_subset = repr_data[:min_samples]
            
            # 预测语义标签
            score = self._predict_factor_from_representation(repr_subset, semantic_subset)
            consistency_scores.append(score)
        
        return np.mean(consistency_scores) if consistency_scores else 0.0

    def _compute_mutual_information(self, representations, labels):
        """
        计算表示和标签之间的互信息
        """
        try:
            if len(representations.shape) == 1:
                representations = representations.reshape(-1, 1)

            # 使用sklearn的互信息估计
            mi_scores = []
            for dim in range(representations.shape[1]):
                mi = mutual_info_regression(
                    representations[:, dim].reshape(-1, 1),
                    labels,
                    random_state=self.random_state
                )[0]
                mi_scores.append(mi)

            return np.mean(mi_scores)
        except:
            return 0.0

    def _compute_entropy(self, labels):
        """
        计算离散标签的熵
        """
        try:
            unique_labels, counts = np.unique(labels, return_counts=True)
            probabilities = counts / len(labels)
            return scipy_entropy(probabilities, base=2)
        except:
            return 1.0

    def _predict_factor_from_representation(self, representations, labels):
        """
        使用表示预测因子标签，返回预测准确性
        """
        try:
            if len(representations) < 10:
                return 0.0

            # 分割训练和测试集
            split_idx = int(0.8 * len(representations))
            X_train, X_test = representations[:split_idx], representations[split_idx:]
            y_train, y_test = labels[:split_idx], labels[split_idx:]

            if len(X_test) == 0:
                return 0.0

            # 判断是分类还是回归任务
            unique_labels = len(np.unique(labels))
            if unique_labels <= 20:  # 分类任务
                model = RandomForestClassifier(n_estimators=50, random_state=self.random_state)
                model.fit(X_train, y_train)
                predictions = model.predict(X_test)
                score = accuracy_score(y_test, predictions)
            else:  # 回归任务
                model = RandomForestRegressor(n_estimators=50, random_state=self.random_state)
                model.fit(X_train, y_train)
                predictions = model.predict(X_test)
                score = r2_score(y_test, predictions)
                score = max(0, score)  # R2可能为负，截断到0

            return score
        except:
            return 0.0

    def _compute_feature_importance(self, representations, labels):
        """
        计算表示对标签的特征重要性
        """
        try:
            if len(representations) < 10:
                return 0.0

            unique_labels = len(np.unique(labels))
            if unique_labels <= 20:  # 分类任务
                model = RandomForestClassifier(n_estimators=50, random_state=self.random_state)
            else:  # 回归任务
                model = RandomForestRegressor(n_estimators=50, random_state=self.random_state)

            model.fit(representations, labels)
            return np.mean(model.feature_importances_)
        except:
            return 0.0

    def _compute_disentanglement(self, importance_matrix):
        """
        计算解耦度：每个因子是否主要由一个表示捕获
        """
        try:
            # 对每个因子，计算最大重要性与其他重要性的比值
            disentanglement_scores = []
            for i in range(importance_matrix.shape[0]):
                importances = importance_matrix[i, :]
                if np.sum(importances) == 0:
                    continue

                # 归一化重要性
                normalized_importances = importances / np.sum(importances)

                # 计算熵（越低越解耦）
                entropy = scipy_entropy(normalized_importances + 1e-12, base=2)
                max_entropy = np.log2(len(normalized_importances))

                # 解耦度 = 1 - 归一化熵
                disentanglement = 1 - (entropy / max_entropy) if max_entropy > 0 else 0
                disentanglement_scores.append(disentanglement)

            return np.mean(disentanglement_scores) if disentanglement_scores else 0.0
        except:
            return 0.0

    def _compute_completeness(self, importance_matrix):
        """
        计算完整性：每个表示是否主要捕获一个因子
        """
        try:
            # 转置矩阵，对每个表示计算
            completeness_scores = []
            for j in range(importance_matrix.shape[1]):
                importances = importance_matrix[:, j]
                if np.sum(importances) == 0:
                    continue

                # 归一化重要性
                normalized_importances = importances / np.sum(importances)

                # 计算熵（越低越完整）
                entropy = scipy_entropy(normalized_importances + 1e-12, base=2)
                max_entropy = np.log2(len(normalized_importances))

                # 完整性 = 1 - 归一化熵
                completeness = 1 - (entropy / max_entropy) if max_entropy > 0 else 0
                completeness_scores.append(completeness)

            return np.mean(completeness_scores) if completeness_scores else 0.0
        except:
            return 0.0
