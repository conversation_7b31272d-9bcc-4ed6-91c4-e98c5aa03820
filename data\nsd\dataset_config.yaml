# Batch configurations
batches:
  # Batch 1: Only fMRI data from subjects 1 and 2
  fmri_only:
    modality_configs:
      - type: fmri
        subjects: [1, 2]
    length: 1000  # Optional: Override length for this batch
  
  # Batch 2: fMRI and EEG data from multiple subjects
  fmri_eeg_combined:
    modality_configs:
      - type: fmri
        subjects: [1, 2, 5, 7]
      - type: eeg
        subjects: [5, 6, 7, 8]
  
  # Batch 3: Only EEG data with image loading
  eeg_with_images:
    modality_configs:
      - type: eeg
        subjects: [7, 8]
        load_image: true
    
  # Batch 4: Single subject fMRI
  single_subject_fmri:
    modality_configs:
      - type: fmri
        subjects: [5]
  
  # Batch 5: Single subject EEG
  single_subject_eeg:
    modality_configs:
      - type: eeg
        subjects: [6]
