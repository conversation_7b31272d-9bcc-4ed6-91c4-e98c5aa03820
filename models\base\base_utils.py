
import torch.nn.functional as F

def cross_entropy_(_input, _target, eps=1e-6):
    """k-Class Cross Entropy (Log Softmax + Log Loss)

    @param input: torch.Tensor (size K x bs x ...) The last dimension contains logit probabilities for each class.
    @param target: torch.Tensor (size bs x ...) The last dimension true probabilities (0 or 1) for each class.
    @param eps: error to add (default: 1e-6)
    @return loss: torch.Tensor same shape as input
    """

    _log_input = F.log_softmax(_input + eps, dim=-1)
    loss = _target * _log_input
    return loss


def cross_entropy(input, target, eps=1e-6):
    """

    Wrapper for the cross_entropy loss handling different inputs / targets types.

    """
    _input = input
    _target = target
    if isinstance(input, dict):
        if "one_hot" in input:
            _input = input["one_hot"]
        else:
            raise NotImplementedError()

    if isinstance(target, dict):
        if "one_hot" in target:
            _target = target["one_hot"]

        elif "tokens" in target:
            # converts to tokens proba instead of class id for text
            _target = F.one_hot(target["tokens"], _input.shape[-1])

    return cross_entropy_(_input, _target, eps)